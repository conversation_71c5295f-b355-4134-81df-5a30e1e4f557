<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 中国REITs论坛</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0px;
            background-image: none;
            position: relative;
            left: -0px;
            width: 1912px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: rgba(242, 242, 242, 1);
            min-height: 954px;
            overflow-x: auto;
        }

        /* 背景层 */
        .background-layer {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 954px;
            background-color: rgba(242, 242, 242, 1);
            z-index: 0;
        }

        /* 半透明遮罩层 */
        .overlay-layer {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 954px;
            background-color: rgba(255, 255, 255, 0.4980392156862745);
            z-index: 1;
        }

        /* 登录容器 - 按照原型位置定位 */
        .login-container {
            position: absolute;
            left: 1172px;
            top: 201px;
            width: 560px;
            height: 551px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            padding: 60px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 2;
        }

        .logo {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo svg {
            width: 120px;
            height: auto;
        }

        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e0e0e0;
        }

        .tab-button {
            flex: 1;
            padding: 12px 0;
            text-align: center;
            background: none;
            border: none;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            position: relative;
            transition: color 0.3s;
        }

        .tab-button.active {
            color: #1868f1;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background-color: #1868f1;
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d7d7d7;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1868f1;
        }

        .verification-group {
            display: flex;
            gap: 12px;
        }

        .verification-input {
            flex: 1;
        }

        .verification-btn {
            padding: 12px 20px;
            background-color: #1868f1;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
            transition: background-color 0.3s;
        }

        .verification-btn:hover {
            background-color: #1557d6;
        }

        .verification-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            border: 1px solid #7f7f7f;
            border-radius: 3px;
            cursor: pointer;
            position: relative;
        }

        .checkbox.checked {
            background-color: #1868f1;
            border-color: #1868f1;
        }

        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
        }

        .forgot-link {
            color: #1868f1;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background-color: #1868f1;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background-color: #1557d6;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #666;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #d7d7d7;
        }

        .divider span {
            background-color: rgba(255, 255, 255, 0.8);
            padding: 0 20px;
        }

        .other-login {
            text-align: center;
        }

        .forum-login-btn {
            color: #1868f1;
            text-decoration: none;
            font-size: 16px;
            padding: 12px 24px;
            border: 1px solid #1868f1;
            border-radius: 8px;
            display: inline-block;
            transition: all 0.3s;
        }

        .forum-login-btn:hover {
            background-color: #1868f1;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .countdown {
            color: #666;
        }

        /* 响应式设计 */
        @media (max-width: 1920px) {
            body {
                width: 100vw;
            }
            
            .background-layer,
            .overlay-layer {
                width: 100vw;
                height: 100vh;
            }
            
            .login-container {
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
    </style>
</head>
<body>
    <!-- 背景层 -->
    <div class="background-layer"></div>
    
    <!-- 半透明遮罩层 -->
    <div class="overlay-layer"></div>
    
    <!-- 登录容器 -->
    <div class="login-container">
        <div class="logo">
            <svg width="120" height="40" viewBox="0 0 120 40">
                <circle cx="20" cy="20" r="18" fill="#1868f1"/>
                <text x="45" y="25" font-family="Arial" font-size="16" font-weight="bold" fill="#333">REITs论坛</text>
            </svg>
        </div>

        <h1 class="login-title">登录</h1>

        <div class="login-tabs">
            <button class="tab-button active" data-tab="password">密码登录</button>
            <button class="tab-button" data-tab="sms">短信登录</button>
        </div>

        <!-- 密码登录 -->
        <div id="password-tab" class="tab-content active">
            <form id="password-form">
                <div class="form-group">
                    <label class="form-label">手机号/邮箱</label>
                    <input type="text" class="form-input" id="username" placeholder="请输入手机号或邮箱" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" id="password" placeholder="请输入密码" required>
                </div>

                <div class="remember-forgot">
                    <div class="checkbox-group">
                        <div class="checkbox" id="remember-checkbox"></div>
                        <label for="remember-checkbox">记住我</label>
                    </div>
                    <a href="#" class="forgot-link">忘记密码？</a>
                </div>

                <button type="submit" class="login-btn">登录</button>
            </form>
        </div>

        <!-- 短信登录 -->
        <div id="sms-tab" class="tab-content">
            <form id="sms-form">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入手机号" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">验证码</label>
                    <div class="verification-group">
                        <input type="text" class="form-input verification-input" id="verification-code" placeholder="请输入验证码" required>
                        <button type="button" class="verification-btn" id="send-code-btn">获取验证码</button>
                    </div>
                </div>

                <button type="submit" class="login-btn">登录</button>
            </form>
        </div>

        <div class="divider">
            <span>其他登录方式</span>
        </div>

        <div class="other-login">
            <a href="#" class="forum-login-btn">中国REITs论坛账号登录</a>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                
                // 移除所有活动状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                
                // 添加当前活动状态
                button.classList.add('active');
                document.getElementById(`${tabName}-tab`).classList.add('active');
            });
        });

        // 记住我复选框
        const rememberCheckbox = document.getElementById('remember-checkbox');
        let isRememberChecked = false;

        rememberCheckbox.addEventListener('click', () => {
            isRememberChecked = !isRememberChecked;
            if (isRememberChecked) {
                rememberCheckbox.classList.add('checked');
            } else {
                rememberCheckbox.classList.remove('checked');
            }
        });

        // 获取验证码功能
        const sendCodeBtn = document.getElementById('send-code-btn');
        let countdown = 0;

        sendCodeBtn.addEventListener('click', () => {
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                alert('请先输入手机号');
                phoneInput.focus();
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号');
                phoneInput.focus();
                return;
            }

            // 开始倒计时
            countdown = 60;
            sendCodeBtn.disabled = true;
            
            const timer = setInterval(() => {
                sendCodeBtn.innerHTML = `<span class="countdown">${countdown}s后重新获取</span>`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.innerHTML = '获取验证码';
                }
            }, 1000);

            // 模拟发送验证码
            console.log('发送验证码到:', phone);
            alert('验证码已发送，请注意查收');
        });

        // 密码登录表单提交
        document.getElementById('password-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                alert('请填写完整的登录信息');
                return;
            }

            // 模拟登录
            console.log('密码登录:', { username, password, remember: isRememberChecked });
            alert('登录成功！');
        });

        // 短信登录表单提交
        document.getElementById('sms-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value.trim();
            const code = document.getElementById('verification-code').value.trim();
            
            if (!phone || !code) {
                alert('请填写完整的登录信息');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号');
                return;
            }

            if (!/^\d{6}$/.test(code)) {
                alert('请输入6位数字验证码');
                return;
            }

            // 模拟登录
            console.log('短信登录:', { phone, code });
            alert('登录成功！');
        });

        // 忘记密码
        document.querySelector('.forgot-link').addEventListener('click', (e) => {
            e.preventDefault();
            alert('忘记密码功能开发中...');
        });

        // 论坛账号登录
        document.querySelector('.forum-login-btn').addEventListener('click', (e) => {
            e.preventDefault();
            alert('跳转到中国REITs论坛登录页面...');
        });
    </script>
</body>
</html>
